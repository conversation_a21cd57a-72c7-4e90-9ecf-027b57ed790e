openapi: 3.0.3

info:
  version: 1.0.0
  title: Corbado generic API entities
  description: Overview of all Corbado generic API entities.
  contact:
    name: Corbado API Team
    email: <EMAIL>
    url: https://www.corbado.com

servers:
  - url: https://api.corbado.com

tags:
  - name: Common
    description: Common entities

paths:
  # This has to be there with all possible schemas references, so that the generator actually generates types for them
  /unused/{sessionID}:
    get:
      description: unused
      operationId: unused
      tags:
        - Common
      security:
        - projectID: [ ]
      parameters:
        - $ref: '#/components/parameters/remoteAddress'
        - $ref: '#/components/parameters/userAgent'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/filter'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/sessionID'
      responses:
        '200':
          description: unused
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/allTypes'

components:
  securitySchemes:
    projectID:
      in: header
      name: X-Corbado-ProjectID
      type: apiKey

  parameters:
    remoteAddress:
      name: remoteAddress
      in: query
      description: C<PERSON>'s remote address
      required: false
      schema:
        type: string

    userAgent:
      name: userAgent
      in: query
      description: Client's user agent
      required: false
      schema:
        type: string

    sort:
      name: sort
      in: query
      description: Field sorting
      required: false
      schema:
        type: string

    filter:
      name: filter[]
      in: query
      description: Field filtering
      required: false
      style: form
      explode: true
      schema:
        type: array
        items:
          type: string
      examples:
        filterEmail:
          summary: Filter for one email address
          value:
            - identifierType:eq:email
            - identifierValue:eq:<EMAIL>
        filterTimepoint:
          summary: timePoint after 20/07/2021
          value:
            - timePoint:gt:2021-07-20T00:00:00

    page:
      name: page
      in: query
      description: Page number
      required: false
      schema:
        type: integer
        default: 1

    pageSize:
      name: pageSize
      in: query
      description: Number of items per page
      required: false
      schema:
        type: integer
        default: 10

    sessionID:
      name: sessionID
      in: path
      description: ID of session
      required: true
      schema:
        type: string
        minLength: 30
        maxLength: 30

  schemas:
    paging:
      type: object
      required:
        - page
        - totalPages
        - totalItems
      properties:
        page:
          description: current page returned in response
          type: integer
          default: 1
        totalPages:
          description: total number of pages available
          type: integer
        totalItems:
          description: total number of items available
          type: integer

    clientInfo:
      type: object
      required:
        - remoteAddress
        - userAgent
      properties:
        remoteAddress:
          description: client's IP address
          type: string
          example: '::ffff:**********'
        userAgent:
          description: client's User Agent
          type: string
          example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

    status:
      type: string
      enum: [ 'active', 'pending', 'deleted' ]
      description: Generic status that can describe Corbado entities

    authMethods:
      type: array
      items:
        $ref: '#/components/schemas/authMethod'

    authMethod:
      type: string
      enum: [ 'email', 'phone_number', 'webauthn', 'password' ]
      description: Authentication methods

    fullUser:
      type: object
      description: User entry with emails and phone numbers
      required:
        - ID
        - name
        - fullName
        - created
        - updated
        - status
        - emails
        - phoneNumbers
        - usernames
        - socialAccounts
      properties:
        ID:
          $ref: '#/components/schemas/userID'
        name:
          type: string
        fullName:
          type: string
        created:
          $ref: '#/components/schemas/created'
        updated:
          $ref: '#/components/schemas/updated'
        status:
          $ref: '#/components/schemas/status'
        emails:
          type: array
          items:
            $ref: '#/components/schemas/userEmail'
        phoneNumbers:
          type: array
          items:
            $ref: '#/components/schemas/userPhoneNumber'
        usernames:
          type: array
          items:
            $ref: '#/components/schemas/userUsername'
        socialAccounts:
          type: array
          items:
            $ref: '#/components/schemas/userSocialAccount'


    userEmail:
      type: object
      description: User's email
      required:
        - ID
        - email
        - created
        - updated
        - status
      properties:
        ID:
          $ref: '#/components/schemas/ID'
        email:
          type: string
        created:
          $ref: '#/components/schemas/created'
        updated:
          $ref: '#/components/schemas/updated'
        status:
          $ref: '#/components/schemas/status'

    userSocialAccount:
      type: object
      description: User's social account
      required:
        - providerType
        - identifierValue
        - avatarUrl
        - fullName
      properties:
        providerType:
          $ref: '#/components/schemas/socialProviderType'
        identifierValue:
          type: string
        avatarUrl:
          type: string
        fullName:
          type: string

    userPhoneNumber:
      type: object
      description: User's phone number
      required:
        - ID
        - phoneNumber
        - created
        - updated
        - status
      properties:
        ID:
          $ref: '#/components/schemas/ID'
        phoneNumber:
          type: string
        created:
          $ref: '#/components/schemas/created'
        updated:
          $ref: '#/components/schemas/updated'
        status:
          $ref: '#/components/schemas/status'

    userUsername:
      type: object
      description: User's username
      required:
        - ID
        - username
        - created
        - updated
        - status
      properties:
        ID:
          $ref: '#/components/schemas/ID'
        username:
          type: string
        created:
          $ref: '#/components/schemas/created'
        updated:
          $ref: '#/components/schemas/updated'
        status:
          $ref: '#/components/schemas/status'

    highEntropyValues:
      description: High entropy values from browser
      type: object
      required:
        - platform
        - platformVersion
        - mobile
      properties:
        platform:
          description: Platform
          type: string
          example: 'macOS'
        platformVersion:
          description: Platform version
          type: string
          example: '14.1.2'
        mobile:
          description: Mobile
          type: boolean

    ID:
      description: generic ID
      type: string

    userID:
      description: ID of the user
      type: string

    deviceID:
      description: ID of the device
      type: string

    emailID:
      description: ID of the email
      type: string

    phoneNumberID:
      description: ID of the phone number
      type: string

    projectID:
      description: ID of project
      type: string

    requestID:
      description: Unique ID of request, you can provide your own while making the request, if not the ID will be randomly generated on server side
      type: string
      example: 'req-557...663'

    emailLinkID:
      description: ID of the email magic link
      type: string

    emailCodeID:
      description: ID of the email OTP
      type: string

    additionalPayload:
      description: Additional payload in JSON format
      type: string
      example: '{"projectAbbreviation":"CRBD"}'

    created:
      description: Timestamp of when the entity was created in yyyy-MM-dd'T'HH:mm:ss format
      type: string

    updated:
      description: Timestamp of when the entity was last updated in yyyy-MM-dd'T'HH:mm:ss format
      type: string

    deleted:
      description: Timestamp of when the entity was deleted in yyyy-MM-dd'T'HH:mm:ss format
      type: string

    loginIdentifierType:
      description: Login Identifier type (deprecated)
      type: string
      enum: [ 'email', 'phone_number', 'custom' ]

    identifierType:
      description: Login Identifier type
      type: string
      enum: [ 'email', 'phone', 'username' ]

    appType:
      description: Application type
      type: string
      enum: [ 'empty', 'web', 'native' ]

    sessionManagement:
      description: What session management should be used
      type: string
      enum: [ 'SessionManagementCorbado', 'SessionManagementOwn' ]

    requestData:
      description: Data about the request itself, can be used for debugging
      type: object
      required:
        - requestID
        - link
      properties:
        requestID:
          $ref: '#/components/schemas/requestID'
        link:
          description: Link to dashboard with details about request
          type: string
          example: 'https://my.corbado.com/requests/req-xxxxxxxxxxxxxxxxxxx'

    loginIdentifierConfig:
      type: object
      required:
        - type
        - enforceVerification
        - useAsLoginIdentifier
      properties:
        type:
          $ref: '#/components/schemas/identifierType'
        enforceVerification:
          type: string
          enum: [ none, signup, at_first_login ]
        useAsLoginIdentifier:
          type: boolean
        metadata:
          type: object

    socialProviderType:
      type: string
      enum: [ 'google', 'microsoft', 'github' ]

    # this is necessary so that code generator doesn't ignore "unused" types
    allTypes:
      type: object
      properties:
        p1:
          $ref: '#/components/schemas/paging'
        p2:
          $ref: '#/components/schemas/clientInfo'
        p3:
          $ref: '#/components/schemas/ID'
        p4:
          $ref: '#/components/schemas/userID'
        p5:
          $ref: '#/components/schemas/emailID'
        p6:
          $ref: '#/components/schemas/emailLinkID'
        p7:
          $ref: '#/components/schemas/phoneNumberID'
        p8:
          $ref: '#/components/schemas/created'
        p9:
          $ref: '#/components/schemas/updated'
        p10:
          $ref: '#/components/schemas/deleted'
        p11:
          $ref: '#/components/schemas/deviceID'
        p12:
          $ref: '#/components/schemas/additionalPayload'
        p13:
          $ref: '#/components/schemas/status'
        p14:
          $ref: '#/components/schemas/projectID'
        p15:
          $ref: '#/components/schemas/requestID'
        p16:
          $ref: '#/components/schemas/errorRsp'
        p17:
          $ref: '#/components/schemas/authMethods'
        p18:
          $ref: '#/components/schemas/fullUser'
        p19:
          $ref: '#/components/schemas/loginIdentifierType'
        p20:
          $ref: '#/components/schemas/emailCodeID'
        p21:
          $ref: '#/components/schemas/appType'
        p22:
          $ref: '#/components/schemas/sessionManagement'
        p23:
          $ref: '#/components/schemas/highEntropyValues'
        p24:
          $ref: '#/components/schemas/loginIdentifierConfig'
        p25:
          $ref: '#/components/schemas/socialProviderType'
        p26:
          $ref: '#/components/schemas/identifierType'

    genericRsp:
      type: object
      required:
        - httpStatusCode
        - message
        - requestData
        - runtime
      properties:
        httpStatusCode:
          description: HTTP status code of operation
          type: integer
          format: int32
          minimum: 200
          maximum: 599
        message:
          type: string
          example: 'OK'
        requestData:
          $ref: '#/components/schemas/requestData'
        runtime:
          description: Runtime in seconds for this request
          type: number
          format: float
          example: 0.06167686

    errorRsp:
      allOf:
        - $ref: '#/components/schemas/genericRsp'
        - type: object
          required:
            - error
          properties:
            data:
              type: object
            error:
              type: object
              required:
                - type
                - links
              properties:
                type:
                  description: Type of error
                  type: string
                details:
                  description: Details of error
                  type: string
                validation:
                  description: Validation errors per field
                  type: array
                  items:
                    type: object
                    required:
                      - field
                      - message
                    properties:
                      field:
                        type: string
                      message:
                        type: string
                links:
                  description: Additional links to help understand the error
                  type: array
                  items:
                    type: string
