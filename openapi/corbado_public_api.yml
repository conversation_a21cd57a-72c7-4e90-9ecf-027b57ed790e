# Todos
#
# - Add examples
openapi: 3.0.3

###################################################################
# General                                                         #
###################################################################
info:
  version: 2.0.4
  title: Corbado Frontend API
  description: Overview of all Corbado Frontend API calls to implement passwordless authentication.
  contact:
    name: Corbado team
    email: <EMAIL>
    url: https://www.corbado.com

servers:
  - url: https://{projectId}.frontendapi.corbado.io
    variables:
      projectId:
        description: Your Corbado project ID
        default: pro-000000

tags:
  - name: Auth
    description: All API calls that are related to an authentication process.
  - name: Configs
    description: All API calls to manage configurations
  - name: Users
    description: All API calls to manage users
  - name: CorbadoConnect
    description: All API calls that are related to a connect process

paths:
  /v2/session-config:
    get:
      summary: Get Session Configuration
      description: Retrieves the session configuration settings
      operationId: GetSessionConfig
      tags:
        - Configs
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/sessionConfigRsp"

  /v2/user-details-config:
    get:
      summary: Get User Details Configuration
      description: Gets configs needed by the UserDetails component
      operationId: GetUserDetailsConfig
      tags:
        - Configs
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/userDetailsConfigRsp"

  /v2/me:
    get:
      summary: Get Current User
      description: Gets current user
      operationId: CurrentUserGet
      tags:
        - Users
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/meRsp"
    patch:
      summary: Update Current User
      description: Updates current user
      operationId: CurrentUserUpdate
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/meUpdateReq"
      responses:
        "200":
          $ref: "#/components/responses/200"
    delete:
      summary: Delete Current User
      description: Deletes current user
      operationId: CurrentUserDelete
      tags:
        - Users
      responses:
        "200":
          $ref: "#/components/responses/200"

  /v2/me/passkeys:
    get:
      summary: Get User Passkeys
      description: Gets current user's passkeys
      operationId: CurrentUserPasskeyGet
      tags:
        - Users
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/mePasskeyRsp"

  /v2/me/passkeys/append/start:
    post:
      summary: Start Passkey Append
      description: Starts passkey append for current user
      operationId: CurrentUserPasskeyAppendStart
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/mePasskeysAppendStartReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/mePasskeysAppendStartRsp"

  /v2/me/passkeys/append/finish:
    post:
      summary: Finish Passkey Append
      description: Finishes passkey append for current user
      operationId: CurrentUserPasskeyAppendFinish
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/mePasskeysAppendFinishReq"
      responses:
        "200":
          description: tbd

  /v2/me/passkeys/{credentialID}:
    delete:
      summary: Delete User Passkey
      description: Delete current user's passkeys
      operationId: CurrentUserPasskeyDelete
      tags:
        - Users
      parameters:
        - in: path
          required: true
          name: credentialID
          schema:
            type: string
          description: "Credential ID from passkeys"
          example: "cre-abc123"
      responses:
        "200":
          description: tbs
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/mePasskeyDeleteRsp"

  /v2/me/refresh:
    post:
      summary: Refresh Session
      description: Performs session refresh
      operationId: CurrentUserSessionRefresh
      tags:
        - Users
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/meRefreshRsp"

  /v2/me/logout:
    post:
      summary: Logout User
      description: Performs session logout
      operationId: CurrentUserSessionLogout
      tags:
        - Users
      responses:
        "204":
          description: tbd

  /v2/me/identifier:
    post:
      summary: Create Identifier
      description: Creates an identifier
      operationId: CurrentUserIdentifierCreate
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/meIdentifierCreateReq"
      responses:
        "200":
          $ref: "#/components/responses/200"
    delete:
      summary: Delete Identifier
      description: Deletes an identifier
      operationId: CurrentUserIdentifierDelete
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/meIdentifierDeleteReq"
      responses:
        "200":
          $ref: "#/components/responses/200"
    patch:
      summary: Update Identifier
      description: Modifies an identifier (only permitted for username; identifierID will change)
      operationId: CurrentUserIdentifierUpdate
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/meIdentifierUpdateReq"
      responses:
        "200":
          $ref: "#/components/responses/200"

  /v2/me/identifier/verify/start:
    post:
      summary: Start Identifier Verification
      description: Creates challenge (only email otp and phone otp supported for now)
      operationId: CurrentUserIdentifierVerifyStart
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/meIdentifierVerifyStartReq"
      responses:
        "200":
          $ref: "#/components/responses/200"

  /v2/me/identifier/verify/finish:
    post:
      summary: Finish Identifier Verification
      description: Verifies challenge
      operationId: CurrentUserIdentifierVerifyFinish
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/meIdentifierVerifyFinishReq"
      responses:
        "200":
          $ref: "#/components/responses/200"

  /v2/auth/process/init:
    post:
      summary: Initialize Authentication Process
      description: Initializes a new authentication process
      operationId: ProcessInit
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/processInitReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processInitRsp"

  /v2/auth/process:
    get:
      summary: Get Authentication Process
      description: Retrieves the current authentication process state
      operationId: ProcessGet
      tags:
        - Auth
      parameters:
        - in: query
          required: false
          name: preferredBlock
          schema:
            $ref: "#/components/schemas/blockType"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/process/complete:
    post:
      summary: Complete Authentication Process
      description: Completes the current authentication process
      operationId: ProcessComplete
      tags:
        - Auth
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/process/reset:
    post:
      summary: Reset Authentication Process
      description: Resets the current authentication process
      operationId: ProcessReset
      tags:
        - Auth
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/signup/init:
    post:
      summary: Initialize Signup
      description: Initializes a new user signup process
      operationId: SignupInit
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/signupInitReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/login/init:
    post:
      summary: Initialize Login
      description: Initializes a new login process
      operationId: LoginInit
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/loginInitReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/block/skip:
    post:
      summary: Skip Authentication Block
      description: Skips the current authentication block
      operationId: BlockSkip
      tags:
        - Auth
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/passkey/append/start:
    post:
      summary: Start Passkey Append
      description: Starts the process of appending a new passkey
      operationId: PasskeyAppendStart
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/passkeyAppendStartReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/passkey/append/finish:
    post:
      summary: Finish Passkey Append
      description: Completes the process of appending a new passkey
      operationId: PasskeyAppendFinish
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/passkeyAppendFinishReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/passkey/login/start:
    post:
      summary: Start Passkey Login
      description: Initiates the passkey login process
      operationId: PasskeyLoginStart
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/passkeyLoginStartReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/passkey/login/finish:
    post:
      summary: Finish Passkey Login
      description: Completes the passkey login process
      operationId: PasskeyLoginFinish
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/passkeyLoginFinishReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/passkey/mediation/finish:
    post:
      summary: Finish Passkey Mediation
      description: Completes the passkey mediation process
      operationId: PasskeyMediationFinish
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/passkeyMediationFinishReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/identifier/verify/start:
    post:
      summary: Start Identifier Verification
      description: Initiates the identifier verification process
      operationId: IdentifierVerifyStart
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/identifierVerifyStartReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/identifier/verify/finish:
    post:
      summary: Finish Identifier Verification
      description: Completes the identifier verification process
      operationId: IdentifierVerifyFinish
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/identifierVerifyFinishReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/identifier/verify/status:
    get:
      summary: Get Identifier Verification Status
      description: Retrieves the current status of identifier verification
      operationId: IdentifierVerifyStatus
      tags:
        - Auth
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/identifier/update:
    post:
      summary: Update Identifier
      description: Updates the user's identifier information
      operationId: IdentifierUpdate
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/identifierUpdateReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/social/verify/start:
    post:
      summary: Start Social Verification
      description: Initiates the social authentication verification process
      operationId: SocialVerifyStart
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/socialVerifyStartReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/social/verify/callback:
    get:
      summary: Social Verification Callback
      description: Handles the callback from social authentication providers
      operationId: SocialVerifyCallback
      tags:
        - Auth
      responses:
        "200":
          description: tbd

  /v2/auth/social/verify/finish:
    post:
      summary: Finish Social Verification
      description: Completes the social authentication verification process
      operationId: SocialVerifyFinish
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/socialVerifyFinishReq"
      responses:
        "200":
          description: tbd
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/processResponse"

  /v2/auth/events:
    post:
      summary: Create Authentication Event
      description: Creates a new user generated complete event
      operationId: EventCreate
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/eventCreateReq"
      responses:
        "204":
          description: tbd

  /v2/connect/login/init:
    post:
      summary: Initialize Connect Login
      description: Initializes a connect process for login
      operationId: ConnectLoginInit
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectLoginInitReq"
      responses:
        "200":
          description: Contains information about if and how a passkey login can be started.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectLoginInitRsp"

  /v2/connect/login/start:
    post:
      summary: Start Connect Login
      description: Starts an initialized connect login process
      operationId: ConnectLoginStart
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectLoginStartReq"
      responses:
        "200":
          description: Contains the challenge for the passkey login.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectLoginStartRsp"
        "404":
          description: No user was found for the identifier

  /v2/connect/login/finish:
    post:
      summary: Finish Connect Login
      description: Finishes an initialized connect login process
      operationId: ConnectLoginFinish
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectLoginFinishReq"
      responses:
        "200":
          description: Data about the passkey login and the session.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectLoginFinishRsp"
        "404":
          description: No credential was found for the identifier

  /v2/connect/append/init:
    post:
      summary: Initialize Connect Append
      description: Initializes a connect process for passkey append
      operationId: ConnectAppendInit
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectAppendInitReq"
      responses:
        "200":
          description: Contains information about if and how a passkey append can be started.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectAppendInitRsp"

  /v2/connect/append/start:
    post:
      summary: Start Connect Append
      description: Starts an initialized connect passkey append process
      operationId: ConnectAppendStart
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectAppendStartReq"
      responses:
        "200":
          description: Contains the challenge for the passkey append.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectAppendStartRsp"

  /v2/connect/append/finish:
    post:
      summary: Finish Connect Append
      description: Finishes an initialized connect passkey append process
      operationId: ConnectAppendFinish
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectAppendFinishReq"
      responses:
        "200":
          description: Data about the new passkey.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectAppendFinishRsp"

  /v2/connect/manage/init:
    post:
      summary: Initialize Connect Management
      description: Initializes a connect process for passkey management
      operationId: ConnectManageInit
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectManageInitReq"
      responses:
        "200":
          description: Contains information about if and how passkey management can be started.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectManageInitRsp"

  /v2/connect/manage/delete:
    post:
      summary: Delete Connect Passkey
      description: Deletes a passkey for a user identified by a connect token
      operationId: ConnectManageDelete
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectManageDeleteReq"
      responses:
        "200":
          description: Contains the ID of the deleted passkey.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectManageDeleteRsp"

  /v2/connect/manage/list:
    post:
      summary: List Connect Passkeys
      description: Lists all passkeys for a user identifier by a connect token
      operationId: ConnectManageList
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectManageListReq"
      responses:
        "200":
          description: Contains all passkeys for a user.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectManageListRsp"

  /v2/connect/events:
    post:
      summary: Create Connect Event
      description: Creates a new user generated connect event
      operationId: ConnectEventCreate
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectEventCreateReq"
      responses:
        "204":
          description: tbd

  /v2/connect/process/clear:
    post:
      summary: Clear Connect Process
      description: Remove process state for a connect process
      operationId: ConnectProcessClear
      tags:
        - CorbadoConnect
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/connectProcessClearReq"
      responses:
        "200":
          description: Contains information about the process state.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/connectProcessClearRsp"
        "404":
          description: No process was found for the token

components:
  ###################################################################
  # Security schemes                                                #
  ###################################################################
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    projectID:
      in: header
      name: X-Corbado-ProjectID
      type: apiKey

  schemas:
    processInitReq:
      type: object
      description: tbd.
      required:
        - clientInformation
      properties:
        clientInformation:
          $ref: "#/components/schemas/clientInformation"
        passkeyAppendShown:
          type: integer
          format: int64
        optOutOfPasskeyAppendAfterHybrid:
          type: boolean
          description: deprecated
        preferredBlock:
          $ref: "#/components/schemas/blockType"

    processInitRsp:
      type: object
      description: tbd.
      required:
        - token
        - expiresAt
        - processResponse
      properties:
        newClientEnvHandle:
          type: string
        token:
          type: string
        expiresAt:
          type: integer
        processResponse:
          $ref: "#/components/schemas/processResponse"

    signupInitReq:
      type: object
      description: tbd.
      required:
        - identifiers
      properties:
        fullName:
          type: string
        identifiers:
          type: array
          items:
            $ref: "#/components/schemas/loginIdentifier"

    #    signupInitRsp:
    #      $ref: '#/components/schemas/blockBody'

    loginInitReq:
      type: object
      description: tbd.
      required:
        - identifierValue
        - isPhone
      properties:
        identifierValue:
          type: string
        isPhone:
          type: boolean

    #    loginInitRsp:
    #      $ref: '#/components/schemas/blockBody'

    passkeyAppendStartReq:
      type: object
      properties:
        clientInformation:
          $ref: "#/components/schemas/clientInformation"

    passkeyAppendFinishReq:
      type: object
      description: tbd.
      required:
        - signedChallenge
      properties:
        signedChallenge:
          type: string

    #    passkeyAppendStartRsp:
    #      $ref: '#/components/schemas/blockBody'

    mePasskeysAppendStartReq:
      type: object
      required:
        - clientInformation
      properties:
        clientInformation:
          $ref: "#/components/schemas/clientInformation"

    mePasskeysAppendStartRsp:
      type: object
      required:
        - attestationOptions
      properties:
        newClientEnvHandle:
          type: string
        appendNotAllowedReason:
          type: string
          enum: ["passkey_already_exists", "passkeys_not_supported"]
        attestationOptions:
          type: string
          example: '{"publicKey":{"challenge":"2m6...0w9/MgW...KE=","rp":{"name":"demo","id":"localhost"},"user":{"name":"<EMAIL>","id":"dXN...zk5"},"pubKeyCredParams":[{"type":"public-key","alg":-7},{"type":"public-key","alg":-35},{"type":"public-key","alg":-36},{"type":"public-key","alg":-257},{"type":"public-key","alg":-258},{"type":"public-key","alg":-259},{"type":"public-key","alg":-37},{"type":"public-key","alg":-38},{"type":"public-key","alg":-39},{"type":"public-key","alg":-8}],"authenticatorSelection":{"authenticatorAttachment":"platform","requireResidentKey":false,"userVerification":"required"},"timeout":60000,"attestation":"none"}}'

    mePasskeysAppendFinishReq:
      type: object
      required:
        - attestationResponse
        - clientInformation
      properties:
        attestationResponse:
          type: string
          example: '{"type":"public-key","id":"JM6...J_Q","rawId":"JM6...J_Q","authenticatorAttachment":null,"response":{"clientDataJSON":"eyJ...ZX0","authenticatorData":"SZY...AAQ","signature":"Ni7...YAg","userHandle":"dXN...zk5"},"clientExtensionResults":{}}'
        clientInformation:
          $ref: "#/components/schemas/clientInformation"

    meIdentifierCreateReq:
      type: object
      required:
        - identifierType
        - value
      properties:
        identifierType:
          $ref: "#/components/schemas/loginIdentifierType"
        value:
          type: string

    meIdentifierDeleteReq:
      type: object
      required:
        - identifierID
      properties:
        identifierID:
          type: string

    meIdentifierUpdateReq:
      type: object
      required:
        - identifierID
        - identifierType
        - value
      properties:
        identifierID:
          type: string
        identifierType:
          $ref: "#/components/schemas/loginIdentifierType"
        value:
          type: string

    meIdentifierVerifyStartReq:
      type: object
      description: tbd.
      required:
        - identifierID
        - clientInformation
      properties:
        identifierID:
          type: string
        clientInformation:
          $ref: "#/components/schemas/clientInformation"

    meIdentifierVerifyFinishReq:
      type: object
      required:
        - identifierID
        - code
      properties:
        identifierID:
          type: string
        code:
          type: string

    #    passkeyAppendFinishRsp:
    #      $ref: '#/components/schemas/blockBody'

    passkeyLoginStartReq:
      type: object
      properties:
        clientInformation:
          $ref: "#/components/schemas/clientInformation"

    #    passkeyLoginStartRsp:
    #      $ref: '#/components/schemas/blockBody'

    passkeyLoginFinishReq:
      type: object
      description: tbd.
      required:
        - signedChallenge
      properties:
        signedChallenge:
          type: string

    passkeyMediationFinishReq:
      type: object
      description: tbd.
      required:
        - signedChallenge
      properties:
        signedChallenge:
          type: string

    identifierVerifyStartReq:
      type: object
      description: tbd.
      required:
        - identifierType
        - verificationType
      properties:
        identifierType:
          $ref: "#/components/schemas/loginIdentifierType"
        verificationType:
          $ref: "#/components/schemas/verificationMethod"

    identifierVerifyFinishReq:
      type: object
      required:
        - code
        - identifierType
        - verificationType
        - isNewDevice
      properties:
        code:
          type: string
        identifierType:
          $ref: "#/components/schemas/loginIdentifierType"
        verificationType:
          $ref: "#/components/schemas/verificationMethod"
        isNewDevice:
          type: boolean

    socialVerifyStartReq:
      type: object
      required:
        - providerType
        - redirectUrl
        - authType
      properties:
        providerType:
          $ref: "common.yml#/components/schemas/socialProviderType"
        redirectUrl:
          type: string
        authType:
          $ref: "#/components/schemas/authType"

    socialVerifyFinishReq:
      type: object

    identifierUpdateReq:
      type: object
      required:
        - value
        - identifierType
      properties:
        identifierType:
          $ref: "#/components/schemas/loginIdentifierType"
        value:
          type: string

    sessionConfigRsp:
      type: object
      required:
        - useSessionManagement
      properties:
        useSessionManagement:
          type: boolean
        shortSessionCookieConfig:
          $ref: "#/components/schemas/shortSessionCookieConfig"
        sessionTokenCookieConfig:
          $ref: "#/components/schemas/sessionTokenCookieConfig"
        frontendApiUrl:
          type: string

    userDetailsConfigRsp:
      type: object
      required:
        - fullNameRequired
        - identifiers
      properties:
        fullNameRequired:
          type: boolean
        identifiers:
          type: array
          items:
            $ref: "#/components/schemas/loginIdentifierConfig"

    mePasskeyRsp:
      type: object
      required:
        - passkeys
        - paging
      properties:
        passkeys:
          type: array
          items:
            $ref: "#/components/schemas/passkey"
        paging:
          $ref: "common.yml#/components/schemas/paging"

    meRefreshRsp:
      type: object
      required:
        - shortSession
        - sessionToken
      properties:
        shortSession:
          type: string
          deprecated: true
        sessionToken:
          type: string

    mePasskeyDeleteRsp:
      type: object
      required:
        - id
      properties:
        id:
          type: string

    connectLoginInitReq:
      type: object
      required:
        - clientInformation
        - flags
      properties:
        clientInformation:
          $ref: "#/components/schemas/clientInformation"
        flags:
          type: object
          additionalProperties:
            type: string
        invitationToken:
          type: string

    connectLoginInitRsp:
      type: object
      required:
        - token
        - expiresAt
        - frontendApiUrl
        - loginAllowed
        - flags
      properties:
        token:
          type: string
        newClientEnvHandle:
          type: string
        expiresAt:
          type: integer
          format: int64
        frontendApiUrl:
          type: string
        loginAllowed:
          type: boolean
        conditionalUIChallenge:
          type: string
        flags:
          type: object
          additionalProperties:
            type: string

    connectLoginStartReq:
      type: object
      required:
        - identifier
        - source
        - loadedMs
      properties:
        identifier:
          type: string
        source:
          type: string
          enum: ["one-tap", "text-field", "error-soft", "error-hard"]
        loadedMs:
          type: integer
          format: int64
        loginConnectToken:
          type: string
        identifierHintAvailable:
          type: boolean
        oneTapMeta:
          $ref: "#/components/schemas/clientStateMeta"

    connectLoginStartRsp:
      type: object
      required:
        - assertionOptions
        - isCDA
        - fallbackOperationError
      properties:
        assertionOptions:
          type: string
        isCDA:
          type: boolean
        fallbackOperationError:
          $ref: "#/components/schemas/fallbackOperationError"
        preferImmediatelyAvailable:
          type: boolean

    connectLoginFinishReq:
      type: object
      required:
        - assertionResponse
        - isConditionalUI
      properties:
        isConditionalUI:
          type: boolean
        assertionResponse:
          type: string
        loadedMs:
          type: integer
          format: int64

    connectLoginFinishRsp:
      type: object
      required:
        - session
        - signedPasskeyData
      properties:
        passkeyOperation:
          $ref: "#/components/schemas/passkeyOperation"
        session:
          type: string
        signedPasskeyData:
          type: string
        fallbackOperationError:
          $ref: "#/components/schemas/fallbackOperationError"

    connectAppendInitReq:
      type: object
      required:
        - clientInformation
        - flags
      properties:
        clientInformation:
          $ref: "#/components/schemas/clientInformation"
        flags:
          type: object
          additionalProperties:
            type: string
        invitationToken:
          type: string

    connectAppendInitRsp:
      type: object
      required:
        - processID
        - expiresAt
        - frontendApiUrl
        - appendAllowed
        - flags
      properties:
        processID:
          type: string
        newClientEnvHandle:
          type: string
        expiresAt:
          type: integer
          format: int64
        frontendApiUrl:
          type: string
        appendAllowed:
          type: boolean
        flags:
          type: object
          additionalProperties:
            type: string

    connectAppendStartReq:
      type: object
      required:
        - appendTokenValue
        - loadedMs
      properties:
        appendTokenValue:
          type: string
        forcePasskeyAppend:
          type: boolean
        loadedMs:
          type: integer
          format: int64

    connectAppendStartRsp:
      type: object
      required:
        - attestationOptions
        - variant
        - isRestrictedBrowser
        - autoAppend
      properties:
        attestationOptions:
          type: string
        variant:
          type: string
          enum:
            - default
            - after-hybrid
            - after-error
        isRestrictedBrowser:
          type: boolean
        autoAppend:
          type: boolean

    connectAppendFinishReq:
      type: object
      required:
        - attestationResponse
      properties:
        attestationResponse:
          type: string

    connectAppendFinishRsp:
      type: object
      required:
        - passkeyOperation
      properties:
        passkeyOperation:
          $ref: "#/components/schemas/passkeyOperation"

    connectManageInitReq:
      type: object
      required:
        - clientInformation
        - flags
      properties:
        clientInformation:
          $ref: "#/components/schemas/clientInformation"
        flags:
          type: object
          additionalProperties:
            type: string
        invitationToken:
          type: string

    connectManageInitRsp:
      type: object
      required:
        - processID
        - expiresAt
        - frontendApiUrl
        - manageAllowed
        - flags
      properties:
        processID:
          type: string
        newClientEnvHandle:
          type: string
        expiresAt:
          type: integer
          format: int64
        frontendApiUrl:
          type: string
        manageAllowed:
          type: boolean
        flags:
          type: object
          additionalProperties:
            type: string

    connectManageDeleteReq:
      type: object
      required:
        - connectToken
        - credentialID
      properties:
        connectToken:
          type: string
        credentialID:
          type: string

    connectManageListReq:
      type: object
      required:
        - connectToken
      properties:
        connectToken:
          type: string

    connectManageListRsp:
      type: object
      required:
        - passkeys
        - rpID
        - userID
      properties:
        passkeys:
          type: array
          items:
            $ref: "#/components/schemas/passkey"
        rpID:
          type: string
        userID:
          type: string

    connectManageDeleteRsp:
      type: object
      required:
        - credentialID
      properties:
        credentialID:
          type: string

    connectEventCreateReq:
      type: object
      required:
        - eventType
      properties:
        eventType:
          $ref: "#/components/schemas/passkeyEventType"
        message:
          type: string
        challenge:
          type: string

    passkey:
      type: object
      required:
        - id
        - credentialID
        - attestationType
        - transport
        - backupEligible
        - backupState
        - authenticatorAAGUID
        - aaguidDetails
        - sourceOS
        - sourceBrowser
        - lastUsed
        - created
        - status
        - createdMs
        - lastUsedMs
        - tags
      properties:
        id:
          type: string
          example: "cre-12345"
        credentialID:
          type: string
        attestationType:
          type: string
        transport:
          type: array
          items:
            type: string
            enum: ["usb", "nfc", "ble", "internal", "hybrid", "smart-card"]
        backupEligible:
          type: boolean
        backupState:
          type: boolean
        authenticatorAAGUID:
          type: string
        sourceOS:
          type: string
        sourceBrowser:
          type: string
        lastUsed:
          type: string
          description: Timestamp of when the passkey was last used in yyyy-MM-dd'T'HH:mm:ss format
        created:
          $ref: "common.yml#/components/schemas/created"
        status:
          type: string
          enum: ["pending", "active"]
          description: "Status"
        aaguidDetails:
          $ref: "#/components/schemas/aaguidDetails"
        createdMs:
          type: integer
          format: int64
          description: Unix timestamp of when the passkey was created (in milliseconds)
        lastUsedMs:
          type: integer
          format: int64
          description: Unix timestamp of when the passkey was last used (in milliseconds)
        tags:
          type: array
          items:
            type: string
          description: "Tags attached to a passkey (e.g. synced or hybrid)"

    connectProcessClearReq:
      type: object
      required:
        - processId
      properties:
        processId:
          type: string

    connectProcessClearRsp:
      type: object
      required:
        - processId
      properties:
        processId:
          type: string

    aaguidDetails:
      type: object
      required:
        - name
        - iconLight
        - iconDark
      properties:
        name:
          type: string
        iconLight:
          type: string
        iconDark:
          type: string

    processResponse:
      type: object
      required:
        - blockBody
        - common
      properties:
        blockBody:
          $ref: "#/components/schemas/blockBody"
        common:
          $ref: "#/components/schemas/processCommon"
        newProcess:
          $ref: "#/components/schemas/processStaticInfo"

    eventCreateReq:
      type: object
      required:
        - eventType
      properties:
        eventType:
          $ref: "#/components/schemas/passkeyEventType"
        challenge:
          type: string

    processStaticInfo:
      type: object
      required:
        - token
        - expiresAt
      properties:
        token:
          type: string
        expiresAt:
          type: integer

    processCommon:
      type: object
      required:
        - appName
        - frontendApiUrl
        - hideBadge
        - environment
      properties:
        appName:
          type: string
        frontendApiUrl:
          type: string
        hideBadge:
          type: boolean
        environment:
          type: string

    shortSessionCookieConfig:
      type: object
      deprecated: true
      required:
        - domain
        - secure
        - sameSite
        - path
        - lifetimeSeconds
      properties:
        domain:
          type: string
        secure:
          type: boolean
        sameSite:
          type: string
          enum: ["lax", "strict", "none"]
        path:
          type: string
        lifetimeSeconds:
          type: integer

    sessionTokenCookieConfig:
      type: object
      required:
        - domain
        - secure
        - sameSite
        - path
        - lifetimeSeconds
      properties:
        domain:
          type: string
        secure:
          type: boolean
        sameSite:
          type: string
          enum: ["lax", "strict", "none"]
        path:
          type: string
        lifetimeSeconds:
          type: integer

    loginIdentifierConfig:
      type: object
      required:
        - type
      properties:
        type:
          $ref: "#/components/schemas/loginIdentifierType"

    blockBody:
      type: object
      required:
        - block
        - data
        - authType
      properties:
        block:
          $ref: "#/components/schemas/blockType"
        authType:
          $ref: "#/components/schemas/authType"
        data:
          type: object
          discriminator:
            propertyName: blockType
          oneOf:
            - $ref: "#/components/schemas/generalBlockPasskeyAppend"
            - $ref: "#/components/schemas/generalBlockPasskeyVerify"
            - $ref: "#/components/schemas/generalBlockVerifyIdentifier"
            - $ref: "#/components/schemas/generalBlockPasskeyAppended"
            - $ref: "#/components/schemas/generalBlockCompleted"
            - $ref: "#/components/schemas/generalBlockSignupInit"
            - $ref: "#/components/schemas/generalBlockLoginInit"
            - $ref: "#/components/schemas/generalBlockPostSignupEmailVerify"
        alternatives:
          type: array
          items:
            $ref: "#/components/schemas/blockBody"
        error:
          $ref: "#/components/schemas/requestError"
        continueOnOtherDevice:
          $ref: "#/components/schemas/continueOnOtherDevice"

    loginIdentifier:
      type: object
      required:
        - type
        - identifier
      properties:
        type:
          $ref: "#/components/schemas/loginIdentifierType"
        identifier:
          type: string

    loginIdentifierWithError:
      type: object
      required:
        - type
        - identifier
      properties:
        type:
          $ref: "#/components/schemas/loginIdentifierType"
        identifier:
          type: string
        error:
          $ref: "#/components/schemas/requestError"

    fullNameWithError:
      type: object
      required:
        - fullName
      properties:
        fullName:
          type: string
        error:
          $ref: "#/components/schemas/requestError"

    loginIdentifierType:
      type: string
      enum: [email, phone, username]

    passkeyEventType:
      type: string
      enum:
        [
          login-explicit-abort,
          login-error,
          login-error-untyped,
          login-error-unexpected,
          login-one-tap-switch,
          login-no-credentials,
          user-append-after-cross-platform-blacklisted,
          user-append-after-login-error-blacklisted,
          append-credential-exists,
          append-explicit-abort,
          append-error,
          append-error-unexpected,
          append-learn-more,
          manage-error-unexpected,
          manage-error,
          manage-learn-more,
          manage-credential-exists,
          local-unlock,
        ]

    blockType:
      type: string
      enum:
        [
          signup-init,
          passkey-append,
          phone-verify,
          email-verify,
          passkey-appended,
          completed,
          social-verify,
          login-init,
          passkey-verify,
          conditional-ui-completed,
          post-signup-email-verify,
          passkey-append-after-hybrid,
        ]

    authType:
      type: string
      enum: [signup, login]

    verificationMethod:
      type: string
      enum: [email-link, email-otp, phone-otp]

    passkeyIconSet:
      type: string
      enum: [default, apple, android, windows]

    generalBlockSignupInit:
      type: object
      required:
        - blockType
        - identifiers
        - socialProviders
      properties:
        blockType:
          type: string
        identifiers:
          type: array
          items:
            $ref: "#/components/schemas/loginIdentifierWithError"
        fullName:
          $ref: "#/components/schemas/fullNameWithError"
        socialData:
          $ref: "#/components/schemas/socialData"
        error:
          $ref: "#/components/schemas/requestError"

    generalBlockLoginInit:
      type: object
      required:
        - blockType
        - identifierValue
        - isPhone
        - isPhoneAvailable
        - isEmailAvailable
        - isUsernameAvailable
        - socialData
      properties:
        blockType:
          type: string
        conditionalUIChallenge:
          type: string
        identifierValue:
          type: string
        isPhone:
          type: boolean
        isPhoneAvailable:
          type: boolean
        isEmailAvailable:
          type: boolean
        isUsernameAvailable:
          type: boolean
        socialData:
          $ref: "#/components/schemas/socialData"
        fieldError:
          $ref: "#/components/schemas/requestError"
        error:
          $ref: "#/components/schemas/requestError"

    generalBlockPasskeyAppend:
      type: object
      required:
        - blockType
        - challenge
        - identifierValue
        - identifierType
        - autoSubmit
        - passkeyIconSet
        - variant
      properties:
        blockType:
          type: string
        challenge:
          type: string
        identifierValue:
          type: string
        identifierType:
          $ref: "#/components/schemas/loginIdentifierType"
        autoSubmit:
          type: boolean
        passkeyIconSet:
          $ref: "#/components/schemas/passkeyIconSet"
        variant:
          type: string
          enum: ["default", "after-hybrid", "after-error", "after-no-credential"]

    generalBlockPasskeyVerify:
      type: object
      required:
        - blockType
        - challenge
        - identifierValue
      properties:
        blockType:
          type: string
        challenge:
          type: string
        identifierValue:
          type: string
        loginHint:
          type: string
          enum: ["cda"]

    generalBlockPasskeyAppended:
      type: object
      required:
        - blockType
      properties:
        blockType:
          type: string

    generalBlockCompleted:
      type: object
      required:
        - blockType
        - shortSession
        - sessionToken
      properties:
        blockType:
          type: string
        longSession:
          type: string
          deprecated: true
          description: This is only set if the project environment is set to 'dev'. If set the UI components will set the longSession in local storage because the cookie dropping will not work in Safari for example ("third-party cookie").
        refreshToken:
          type: string
          description: This is only set if the project environment is set to 'dev'. If set the UI components will set the longSession in local storage because the cookie dropping will not work in Safari for example ("third-party cookie").
        shortSession:
          type: string
          deprecated: true
        sessionToken:
          type: string
        passkeyOperation:
          $ref: "#/components/schemas/passkeyOperation"

    generalBlockVerifyIdentifier:
      type: object
      required:
        - verificationMethod
        - identifier
        - alternativeVerificationMethods
        - isPostLoginVerification
        - blockType
      properties:
        blockType:
          type: string
        verificationMethod:
          $ref: "#/components/schemas/verificationMethod"
        identifier:
          type: string
        retryNotBefore:
          type: integer
          format: int32
        error:
          $ref: "#/components/schemas/requestError"
        alternativeVerificationMethods:
          type: array
          items:
            type: object
            required:
              - verificationMethod
              - identifier
            properties:
              verificationMethod:
                $ref: "#/components/schemas/verificationMethod"
              identifier:
                type: string
        isPostLoginVerification:
          type: boolean

    generalBlockPostSignupEmailVerify:
      type: object
      required:
        - blockType
      properties:
        blockType:
          type: string
        error:
          $ref: "#/components/schemas/requestError"

    clientInformation:
      type: object
      properties:
        bluetoothAvailable:
          type: boolean
        clientEnvHandle:
          type: string
        visitorId:
          type: string
        canUsePasskeys:
          type: boolean
          description: Deprecated, use isUserVerifyingPlatformAuthenticatorAvailable instead
        isUserVerifyingPlatformAuthenticatorAvailable:
          type: boolean
        isConditionalMediationAvailable:
          type: boolean
        clientCapabilities:
          $ref: "#/components/schemas/clientCapabilities"
        javaScriptHighEntropy:
          $ref: "#/components/schemas/javaScriptHighEntropy"
        isNative:
          type: boolean
        webdriver:
          type: boolean
        privateMode:
          type: boolean
        clientEnvHandleMeta:
          $ref: "#/components/schemas/clientStateMeta"
        nativeMeta:
          $ref: "#/components/schemas/nativeMeta"

    clientStateMeta:
      type: object
      required:
        - ts
        - source
      properties:
        ts:
          type: integer
          format: int64
        source:
          type: string
          enum: ["ls", "url", "native"]

    nativeMeta:
      type: object
      required:
        - platform
        - platformVersion
        - displayName
      properties:
        platform:
          type: string
        platformVersion:
          type: string
        name:
          type: string
        version:
          type: string
        displayName:
          type: string
        build:
          type: string
        deviceOwnerAuth:
          type: string
          enum: ["none", "code", "biometrics"]
        isBluetoothAvailable:
          type: boolean
        isBluetoothOn:
          type: boolean
        isGooglePlayServices:
          type: boolean
        isDeviceSecure:
          type: boolean
        error:
          type: string

    clientCapabilities:
      type: object
      properties:
        conditionalCreate:
          type: boolean
        conditionalMediation:
          type: boolean
        hybridTransport:
          type: boolean
        passkeyPlatformAuthenticator:
          type: boolean
        userVerifyingPlatformAuthenticator:
          type: boolean

    javaScriptHighEntropy:
      type: object
      required:
        - platform
        - platformVersion
        - mobile
      properties:
        platform:
          type: string
        platformVersion:
          type: string
        mobile:
          type: boolean

    passkeyOperation:
      type: object
      required:
        - operationType
        - identifierValue
        - identifierType
        - ceremonyType
      properties:
        operationType:
          type: string
          enum: ["append", "verify"]
        identifierValue:
          type: string
        identifierType:
          $ref: "#/components/schemas/loginIdentifierType"
        ceremonyType:
          type: string
          enum: ["local", "cda", "security-key"]
        aaguidDetails:
          $ref: "#/components/schemas/aaguidDetails"

    continueOnOtherDevice:
      type: object
      required:
        - reason
      properties:
        reason:
          type: string
          enum: ["email-link-verified", "process-already-completed"]

    meRsp:
      type: object
      required:
        - id
        - fullName
        - identifiers
        - socialAccounts
      properties:
        id:
          type: string
        fullName:
          type: string
        identifiers:
          type: array
          items:
            $ref: "#/components/schemas/identifier"
        socialAccounts:
          type: array
          items:
            $ref: "#/components/schemas/socialAccount"

    meUpdateReq:
      type: object
      required:
        - fullName
      properties:
        fullName:
          type: string

    identifier:
      type: object
      required:
        - id
        - value
        - type
        - status
      properties:
        id:
          type: string
        value:
          type: string
        type:
          $ref: "#/components/schemas/loginIdentifierType"
        status:
          type: string

    socialAccount:
      type: object
      required:
        - providerType
        - identifierValue
        - avatarUrl
        - fullName
      properties:
        providerType:
          $ref: "common.yml#/components/schemas/socialProviderType"
        identifierValue:
          type: string
        avatarUrl:
          type: string
        fullName:
          type: string

    socialData:
      type: object
      required:
        - providers
        - status
      properties:
        providers:
          type: array
          items:
            $ref: "common.yml#/components/schemas/socialProviderType"
        status:
          type: string
          enum: ["initial", "started", "finished"]
        oauthUrl:
          type: string
        error:
          $ref: "#/components/schemas/requestError"

    requestError:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
        message:
          type: string

    fallbackOperationError:
      type: object
      required:
        - initFallback
      properties:
        initFallback:
          type: boolean
        identifier:
          type: string
        error:
          $ref: "#/components/schemas/requestError"

  responses:
    "200":
      description: Operation succeeded
      content:
        application/json:
          schema:
            $ref: "common.yml#/components/schemas/genericRsp"

security:
  - bearerAuth: []
  - projectID: []
